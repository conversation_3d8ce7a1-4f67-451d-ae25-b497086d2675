#!/usr/bin/env python3
"""
Google AI全面诊断测试
分析Google AI无法使用的具体原因
"""

import os
import sys
import json
import time
import socket
import requests
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv(project_root / ".env", override=True)

def test_network_connectivity():
    """测试网络连接性"""
    print("🌐 测试网络连接性")
    print("=" * 60)
    
    # 测试目标
    targets = [
        ("Google DNS", "*******", 53),
        ("Google.com", "www.google.com", 443),
        ("Google AI API", "generativelanguage.googleapis.com", 443),
        ("Google AI Studio", "ai.google.dev", 443),
        ("Google APIs", "googleapis.com", 443)
    ]
    
    results = {}
    
    for name, host, port in targets:
        try:
            print(f"📡 测试 {name} ({host}:{port})...")
            
            # DNS解析测试
            try:
                ip = socket.gethostbyname(host)
                print(f"   DNS解析: ✅ {host} -> {ip}")
                dns_ok = True
            except Exception as e:
                print(f"   DNS解析: ❌ {e}")
                dns_ok = False
                ip = None
            
            # 端口连接测试
            if dns_ok:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(10)
                    result = sock.connect_ex((host, port))
                    sock.close()
                    
                    if result == 0:
                        print(f"   端口连接: ✅ {host}:{port}")
                        port_ok = True
                    else:
                        print(f"   端口连接: ❌ 连接失败 (错误码: {result})")
                        port_ok = False
                except Exception as e:
                    print(f"   端口连接: ❌ {e}")
                    port_ok = False
            else:
                port_ok = False
            
            results[name] = {
                "host": host,
                "port": port,
                "ip": ip,
                "dns_ok": dns_ok,
                "port_ok": port_ok,
                "overall": dns_ok and port_ok
            }
            
        except Exception as e:
            print(f"❌ 测试 {name} 失败: {e}")
            results[name] = {"overall": False, "error": str(e)}
    
    # 总结网络连接性
    print(f"\n📊 网络连接性总结:")
    success_count = sum(1 for r in results.values() if r.get("overall", False))
    print(f"   成功连接: {success_count}/{len(targets)}")
    
    for name, result in results.items():
        status = "✅" if result.get("overall", False) else "❌"
        print(f"   {name}: {status}")
    
    return results

def test_proxy_configuration():
    """测试代理配置"""
    print("\n🔧 测试代理配置")
    print("=" * 60)
    
    # 获取代理配置
    proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
    proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
    full_proxy_url = f"{proxy_url}:{proxy_port}"
    
    print(f"配置的代理: {full_proxy_url}")
    
    # 检查环境变量
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    print(f"\n环境变量状态:")
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"   {var}: ✅ {value}")
        else:
            print(f"   {var}: ❌ 未设置")
    
    # 测试代理连接
    print(f"\n📡 测试代理连接...")
    
    # 设置代理
    os.environ['HTTP_PROXY'] = full_proxy_url
    os.environ['HTTPS_PROXY'] = full_proxy_url
    
    proxies = {
        'http': full_proxy_url,
        'https': full_proxy_url
    }
    
    test_urls = [
        "http://httpbin.org/ip",  # 显示IP地址
        "https://www.google.com",
        "https://ai.google.dev",
        "https://generativelanguage.googleapis.com"
    ]
    
    proxy_results = {}
    
    for url in test_urls:
        try:
            print(f"   测试访问: {url}")
            
            # 不使用代理的请求
            try:
                response_direct = requests.get(url, timeout=5, headers={'User-Agent': 'TradingAgents-CN/1.0'})
                direct_status = response_direct.status_code
                direct_ok = direct_status == 200
            except Exception as e:
                direct_status = f"Error: {e}"
                direct_ok = False
            
            # 使用代理的请求
            try:
                response_proxy = requests.get(
                    url, 
                    proxies=proxies, 
                    timeout=10,
                    headers={'User-Agent': 'TradingAgents-CN/1.0'}
                )
                proxy_status = response_proxy.status_code
                proxy_ok = proxy_status == 200
                
                # 如果是IP检查服务，显示IP
                if "httpbin.org/ip" in url and proxy_ok:
                    ip_info = response_proxy.json()
                    print(f"     代理IP: {ip_info.get('origin', 'Unknown')}")
                    
            except Exception as e:
                proxy_status = f"Error: {e}"
                proxy_ok = False
            
            print(f"     直接访问: {direct_status} {'✅' if direct_ok else '❌'}")
            print(f"     代理访问: {proxy_status} {'✅' if proxy_ok else '❌'}")
            
            proxy_results[url] = {
                "direct_ok": direct_ok,
                "proxy_ok": proxy_ok,
                "direct_status": direct_status,
                "proxy_status": proxy_status
            }
            
        except Exception as e:
            print(f"     测试失败: {e}")
            proxy_results[url] = {"error": str(e)}
    
    return proxy_results

def test_google_api_key():
    """测试Google API密钥"""
    print("\n🔑 测试Google API密钥")
    print("=" * 60)
    
    google_api_key = os.getenv('GOOGLE_API_KEY')
    
    if not google_api_key:
        print("❌ Google API密钥未配置")
        return False
    
    if google_api_key == 'your_google_api_key_here':
        print("❌ Google API密钥为默认值，未实际配置")
        return False
    
    print(f"✅ Google API密钥已配置: {google_api_key[:20]}...")
    
    # 验证API密钥格式
    if google_api_key.startswith('AIza') and len(google_api_key) == 39:
        print("✅ API密钥格式正确 (AIza开头，39字符)")
        format_ok = True
    else:
        print(f"⚠️ API密钥格式可能有问题 (长度: {len(google_api_key)})")
        format_ok = False
    
    # 测试API密钥有效性
    print("\n📝 测试API密钥有效性...")
    
    try:
        # 使用requests直接调用API
        url = "https://generativelanguage.googleapis.com/v1beta/models"
        headers = {
            'x-goog-api-key': google_api_key,
            'Content-Type': 'application/json'
        }
        
        # 设置代理
        proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
        proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
        full_proxy_url = f"{proxy_url}:{proxy_port}"
        
        proxies = {
            'http': full_proxy_url,
            'https': full_proxy_url
        }
        
        print(f"   请求URL: {url}")
        print(f"   使用代理: {full_proxy_url}")
        
        response = requests.get(url, headers=headers, proxies=proxies, timeout=30)
        
        print(f"   响应状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ API密钥有效，获取到 {len(models.get('models', []))} 个模型")
            
            # 显示前几个模型
            if 'models' in models:
                print("   可用模型:")
                for i, model in enumerate(models['models'][:3]):
                    print(f"     {i+1}. {model.get('name', 'Unknown')}")
            
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API密钥测试失败: {e}")
        return False

def test_google_ai_libraries():
    """测试Google AI相关库"""
    print("\n📚 测试Google AI相关库")
    print("=" * 60)
    
    libraries = [
        ("google-generativeai", "google.generativeai"),
        ("langchain-google-genai", "langchain_google_genai"),
        ("langchain-google-vertexai", "langchain_google_vertexai"),
        ("httpx", "httpx"),
        ("requests", "requests")
    ]
    
    library_results = {}
    
    for lib_name, import_name in libraries:
        try:
            print(f"📦 测试 {lib_name}...")
            
            # 尝试导入
            __import__(import_name)
            print(f"   导入: ✅")
            
            # 获取版本信息
            try:
                if import_name == "google.generativeai":
                    import google.generativeai as genai
                    version = getattr(genai, '__version__', 'Unknown')
                elif import_name == "langchain_google_genai":
                    import langchain_google_genai
                    version = getattr(langchain_google_genai, '__version__', 'Unknown')
                elif import_name == "httpx":
                    import httpx
                    version = httpx.__version__
                elif import_name == "requests":
                    import requests
                    version = requests.__version__
                else:
                    version = "Unknown"
                
                print(f"   版本: {version}")
                
            except Exception as e:
                print(f"   版本获取失败: {e}")
                version = "Unknown"
            
            library_results[lib_name] = {
                "available": True,
                "version": version
            }
            
        except ImportError as e:
            print(f"   导入: ❌ {e}")
            library_results[lib_name] = {
                "available": False,
                "error": str(e)
            }
        except Exception as e:
            print(f"   测试失败: ❌ {e}")
            library_results[lib_name] = {
                "available": False,
                "error": str(e)
            }

    return library_results

def test_google_ai_direct_api():
    """测试Google AI直接API调用"""
    print("\n🧪 测试Google AI直接API调用")
    print("=" * 60)
    
    try:
        import google.generativeai as genai
        
        # 配置API密钥
        google_api_key = os.getenv('GOOGLE_API_KEY')
        genai.configure(api_key=google_api_key)
        
        # 设置代理环境变量
        proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
        proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
        full_proxy_url = f"{proxy_url}:{proxy_port}"
        
        os.environ['HTTP_PROXY'] = full_proxy_url
        os.environ['HTTPS_PROXY'] = full_proxy_url
        
        print(f"使用代理: {full_proxy_url}")
        
        # 测试1: 列出模型
        print("\n📝 测试1: 列出可用模型...")
        try:
            models = list(genai.list_models())
            print(f"✅ 成功获取 {len(models)} 个模型")
            
            for i, model in enumerate(models[:3]):
                print(f"   {i+1}. {model.name}")
                
            list_models_ok = True
        except Exception as e:
            print(f"❌ 列出模型失败: {e}")
            list_models_ok = False
        
        # 测试2: 生成内容
        print("\n📝 测试2: 生成内容...")
        try:
            model = genai.GenerativeModel('gemini-2.0-flash')
            
            response = model.generate_content(
                "Say hello in Chinese",
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,
                    max_output_tokens=50
                )
            )
            
            if response and response.text:
                print(f"✅ 内容生成成功")
                print(f"   响应: {response.text}")
                generate_ok = True
            else:
                print(f"❌ 内容生成失败: 无响应")
                generate_ok = False
                
        except Exception as e:
            print(f"❌ 内容生成失败: {e}")
            generate_ok = False
        
        return {
            "list_models": list_models_ok,
            "generate_content": generate_ok
        }
        
    except Exception as e:
        print(f"❌ Google AI直接API测试失败: {e}")
        return {"error": str(e)}

def test_langchain_integration():
    """测试LangChain集成"""
    print("\n🔗 测试LangChain集成")
    print("=" * 60)
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        google_api_key = os.getenv('GOOGLE_API_KEY')
        
        # 设置代理环境变量
        proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
        proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
        full_proxy_url = f"{proxy_url}:{proxy_port}"
        
        os.environ['HTTP_PROXY'] = full_proxy_url
        os.environ['HTTPS_PROXY'] = full_proxy_url
        
        print(f"使用代理: {full_proxy_url}")
        
        # 测试不同模型
        models_to_test = [
            "gemini-2.0-flash",
            "gemini-1.5-pro",
            "gemini-1.5-flash"
        ]
        
        langchain_results = {}
        
        for model_name in models_to_test:
            print(f"\n📝 测试模型: {model_name}")
            
            try:
                llm = ChatGoogleGenerativeAI(
                    model=model_name,
                    google_api_key=google_api_key,
                    temperature=0.1,
                    max_tokens=50
                )
                
                response = llm.invoke("Say 'Hello' in Chinese")
                
                if response and response.content:
                    print(f"✅ {model_name} 调用成功")
                    print(f"   响应: {response.content}")
                    langchain_results[model_name] = True
                else:
                    print(f"❌ {model_name} 调用失败: 无响应")
                    langchain_results[model_name] = False
                    
            except Exception as e:
                print(f"❌ {model_name} 调用失败: {e}")
                langchain_results[model_name] = False
        
        return langchain_results
        
    except Exception as e:
        print(f"❌ LangChain集成测试失败: {e}")
        return {"error": str(e)}

def analyze_results_and_provide_solutions(results):
    """分析结果并提供解决方案"""
    print("\n🔍 问题分析和解决方案")
    print("=" * 60)
    
    # 分析网络连接
    network_results = results.get('network', {})
    google_ai_reachable = network_results.get('Google AI API', {}).get('overall', False)
    
    # 分析代理
    proxy_results = results.get('proxy', {})
    proxy_working = any(r.get('proxy_ok', False) for r in proxy_results.values())
    
    # 分析API密钥
    api_key_valid = results.get('api_key', False)
    
    # 分析库
    library_results = results.get('libraries', {})
    all_libs_available = all(r.get('available', False) for r in library_results.values())
    
    # 分析API调用
    direct_api_results = results.get('direct_api', {})
    langchain_results = results.get('langchain', {})
    
    print("📊 问题诊断:")
    print(f"   网络连接到Google AI: {'✅' if google_ai_reachable else '❌'}")
    print(f"   代理功能: {'✅' if proxy_working else '❌'}")
    print(f"   API密钥有效: {'✅' if api_key_valid else '❌'}")
    print(f"   必要库可用: {'✅' if all_libs_available else '❌'}")
    
    # 提供具体解决方案
    print("\n💡 解决方案建议:")
    
    if not google_ai_reachable:
        print("\n🌐 网络连接问题:")
        print("   - Google AI API端点无法直接访问")
        print("   - 这是中国地区的常见问题")
        print("   - 解决方案:")
        print("     1. 确保代理服务器正常运行")
        print("     2. 使用更稳定的代理服务")
        print("     3. 考虑使用VPN + 代理组合")
    
    if not proxy_working:
        print("\n🔧 代理配置问题:")
        print("   - 代理服务器可能未运行或配置错误")
        print("   - 解决方案:")
        print("     1. 检查代理服务器状态: netstat -an | findstr 7890")
        print("     2. 验证代理配置: curl -x http://127.0.0.1:7890 https://www.google.com")
        print("     3. 尝试不同的代理端口或服务")
    
    if not api_key_valid:
        print("\n🔑 API密钥问题:")
        print("   - API密钥可能无效或已过期")
        print("   - 解决方案:")
        print("     1. 重新生成API密钥: https://aistudio.google.com/")
        print("     2. 检查API密钥权限和配额")
        print("     3. 确认API密钥格式正确")
    
    if not all_libs_available:
        print("\n📚 依赖库问题:")
        print("   - 缺少必要的Python库")
        print("   - 解决方案:")
        print("     1. 安装缺失的库:")
        for lib_name, lib_info in library_results.items():
            if not lib_info.get('available', False):
                print(f"        pip install {lib_name}")
    
    # 地理位置限制分析
    if 'location is not supported' in str(results).lower():
        print("\n🌍 地理位置限制问题:")
        print("   - Google AI检测到不支持的地理位置")
        print("   - 这是最主要的问题")
        print("   - 解决方案:")
        print("     1. 使用Google Cloud Vertex AI (推荐)")
        print("     2. 在海外服务器部署")
        print("     3. 使用替代AI服务:")
        print("        - 阿里百炼 (已配置)")
        print("        - DeepSeek (国内服务)")
        print("        - OpenRouter (多模型支持)")
    
    # 最终建议
    print("\n🎯 最终建议:")
    
    if not google_ai_reachable and not api_key_valid:
        print("   ❌ Google AI在当前环境下无法使用")
        print("   ✅ 强烈建议使用替代方案:")
        print("      1. 阿里百炼 - 完美的中文支持，已配置")
        print("      2. DeepSeek - 国内服务，性价比高")
        print("      3. OpenRouter - 支持多种模型")
    elif google_ai_reachable and api_key_valid:
        print("   ✅ Google AI可能可以使用，但需要稳定的代理")
        print("   💡 建议优化代理配置或考虑海外部署")
    else:
        print("   ⚠️ 部分问题可以解决，但整体使用体验可能不佳")
        print("   💡 建议优先使用国内AI服务")

def main():
    """主函数"""
    print("🔍 Google AI全面诊断测试")
    print("=" * 70)
    
    results = {}
    
    # 1. 网络连接性测试
    results['network'] = test_network_connectivity()
    
    # 2. 代理配置测试
    results['proxy'] = test_proxy_configuration()
    
    # 3. API密钥测试
    results['api_key'] = test_google_api_key()
    
    # 4. 库测试
    results['libraries'] = test_google_ai_libraries()
    
    # 5. 直接API测试
    results['direct_api'] = test_google_ai_direct_api()
    
    # 6. LangChain集成测试
    results['langchain'] = test_langchain_integration()
    
    # 7. 分析结果并提供解决方案
    analyze_results_and_provide_solutions(results)
    
    # 保存详细结果
    results_file = project_root / "google_ai_diagnosis_results.json"
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n📄 详细结果已保存到: {results_file}")
    except Exception as e:
        print(f"\n⚠️ 保存结果失败: {e}")

if __name__ == "__main__":
    main()
