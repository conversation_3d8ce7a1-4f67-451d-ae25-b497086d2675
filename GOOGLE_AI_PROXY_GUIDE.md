
# Google AI代理使用指南

## 🎯 配置要求

1. **代理服务器**: 确保代理服务器运行在端口7890
2. **API密钥**: 在.env文件中配置有效的Google API密钥
3. **网络环境**: 确保代理可以访问Google服务

## 🚀 使用方法

### 1. Web界面使用
```bash
python -m streamlit run web/app.py
```
- 在左侧边栏选择"Google AI - Gemini模型"
- 选择模型: gemini-2.0-flash (推荐)
- 启用记忆功能
- 开始分析

### 2. CLI使用
```bash
python -m cli.main --llm-provider google --model gemini-2.0-flash --stock AAPL
```

### 3. Python API使用
```python
from tradingagents.llm_adapters.google_adapter import ChatGoogleGenerativeAIWithProxy

llm = ChatGoogleGenerativeAIWithProxy(
    model="gemini-2.0-flash",
    proxy_url="http://127.0.0.1",
    proxy_port=7890
)

response = llm.invoke("分析苹果公司的投资价值")
```

## ⚠️ 注意事项

1. **代理稳定性**: 确保代理服务器稳定运行
2. **API限制**: 注意Google AI的API调用限制
3. **网络延迟**: 通过代理访问可能有额外延迟
4. **成本控制**: 监控API使用量和成本

## 🔧 故障排除

1. **连接失败**: 检查代理服务器状态
2. **API错误**: 验证API密钥有效性
3. **地区限制**: 确保代理IP地址支持Google AI服务
