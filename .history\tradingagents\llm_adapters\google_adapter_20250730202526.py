"""
Google AI (Gemini) 适配器，支持代理配置
支持通过HTTP代理访问Google AI API
"""

import os
import logging
import httpx
from typing import Optional, Dict, Any, List
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import BaseMessage
import google.generativeai as genai

logger = logging.getLogger(__name__)


class ChatGoogleGenerativeAIWithProxy(ChatGoogleGenerativeAI):
    """支持代理的Google AI适配器"""
    
    def __init__(
        self,
        model: str = "gemini-2.0-flash",
        google_api_key: Optional[str] = None,
        temperature: float = 0.1,
        max_tokens: Optional[int] = 2000,
        proxy_url: Optional[str] = None,
        proxy_port: Optional[int] = None,
        **kwargs
    ):
        """
        初始化支持代理的Google AI适配器
        
        Args:
            model: 模型名称
            google_api_key: Google API密钥
            temperature: 温度参数
            max_tokens: 最大token数
            proxy_url: 代理URL (如: http://127.0.0.1)
            proxy_port: 代理端口 (如: 7890)
            **kwargs: 其他参数
        """
        
        # 获取API密钥
        if google_api_key is None:
            google_api_key = os.getenv('GOOGLE_API_KEY')
            if not google_api_key:
                raise ValueError(
                    "Google API密钥未找到。"
                    "请设置GOOGLE_API_KEY环境变量或传入google_api_key参数。"
                )
        
        # 设置代理
        self.proxy_url = proxy_url or os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
        self.proxy_port = proxy_port or int(os.getenv('HTTP_PROXY_PORT', '7890'))
        
        # 构建完整的代理URL
        if self.proxy_url and self.proxy_port:
            full_proxy_url = f"{self.proxy_url}:{self.proxy_port}"
            logger.info(f"🌐 配置Google AI代理: {full_proxy_url}")
            
            # 设置环境变量代理
            os.environ['HTTP_PROXY'] = full_proxy_url
            os.environ['HTTPS_PROXY'] = full_proxy_url
            
            # 配置Google AI库的代理
            self._configure_google_ai_proxy(full_proxy_url)
        
        # 调用父类初始化
        super().__init__(
            model=model,
            google_api_key=google_api_key,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
        
        logger.info(f"✅ Google AI适配器初始化成功")
        logger.info(f"   模型: {model}")
        logger.info(f"   代理: {full_proxy_url if self.proxy_url and self.proxy_port else '未配置'}")
    
    def _configure_google_ai_proxy(self, proxy_url: str):
        """配置Google AI库的代理设置"""
        try:
            # 配置google.generativeai的代理
            # 注意：google.generativeai库内部使用requests，我们通过环境变量设置代理
            
            # 创建自定义的httpx客户端（如果需要）
            proxy_config = {
                'http://': proxy_url,
                'https://': proxy_url
            }
            
            # 设置代理环境变量
            os.environ.setdefault('HTTP_PROXY', proxy_url)
            os.environ.setdefault('HTTPS_PROXY', proxy_url)
            
            logger.info(f"🔧 Google AI代理配置完成: {proxy_url}")
            
        except Exception as e:
            logger.warning(f"⚠️ 配置Google AI代理时出现警告: {e}")
    
    def test_connection(self) -> bool:
        """测试Google AI连接"""
        try:
            logger.info("🧪 测试Google AI连接...")
            
            # 配置genai
            genai.configure(api_key=self.google_api_key)
            
            # 尝试列出模型
            models = list(genai.list_models())
            
            if models:
                logger.info(f"✅ Google AI连接成功，发现 {len(models)} 个可用模型")
                return True
            else:
                logger.error("❌ Google AI连接失败：未发现可用模型")
                return False
                
        except Exception as e:
            logger.error(f"❌ Google AI连接测试失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": "Google AI",
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "proxy_url": f"{self.proxy_url}:{self.proxy_port}" if self.proxy_url and self.proxy_port else None,
            "supports_tools": True,
            "supports_streaming": True,
            "context_length": "1M+" if "2.0" in self.model else "128K"
        }


def create_google_ai_with_proxy(
    model: str = "gemini-2.0-flash",
    api_key: Optional[str] = None,
    temperature: float = 0.1,
    max_tokens: Optional[int] = 2000,
    proxy_url: Optional[str] = None,
    proxy_port: Optional[int] = None,
    **kwargs
) -> ChatGoogleGenerativeAIWithProxy:
    """
    创建支持代理的Google AI实例的便捷函数
    
    Args:
        model: 模型名称
        api_key: API密钥
        temperature: 温度参数
        max_tokens: 最大token数
        proxy_url: 代理URL
        proxy_port: 代理端口
        **kwargs: 其他参数
    
    Returns:
        支持代理的Google AI实例
    """
    
    return ChatGoogleGenerativeAIWithProxy(
        model=model,
        google_api_key=api_key,
        temperature=temperature,
        max_tokens=max_tokens,
        proxy_url=proxy_url,
        proxy_port=proxy_port,
        **kwargs
    )


# 支持的Google AI模型配置
GOOGLE_AI_MODELS = {
    "gemini-2.0-flash": {
        "description": "Gemini 2.0 Flash - 最新最快模型",
        "context_length": "1M+",
        "supports_function_calling": True,
        "recommended_for": ["快速响应", "实时分析", "日常对话"]
    },
    "gemini-1.5-pro": {
        "description": "Gemini 1.5 Pro - 强大性能模型",
        "context_length": "128K",
        "supports_function_calling": True,
        "recommended_for": ["深度分析", "复杂推理", "专业任务"]
    },
    "gemini-1.5-flash": {
        "description": "Gemini 1.5 Flash - 快速响应模型",
        "context_length": "128K",
        "supports_function_calling": True,
        "recommended_for": ["快速分析", "简单任务", "批量处理"]
    }
}


def get_available_google_models() -> Dict[str, Dict[str, Any]]:
    """获取可用的Google AI模型列表"""
    return GOOGLE_AI_MODELS
