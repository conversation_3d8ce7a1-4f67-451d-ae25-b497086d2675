#!/usr/bin/env python3
"""
简单的代理测试脚本
测试通过代理访问Google AI
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv(project_root / ".env", override=True)

def test_basic_proxy():
    """测试基本代理连接"""
    print("🌐 测试基本代理连接")
    print("=" * 50)
    
    # 获取代理配置
    proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
    proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
    full_proxy_url = f"{proxy_url}:{proxy_port}"
    
    print(f"代理地址: {full_proxy_url}")
    
    try:
        import requests
        
        # 测试代理连接
        proxies = {
            'http': full_proxy_url,
            'https': full_proxy_url
        }
        
        print("📡 测试访问Google...")
        response = requests.get(
            "https://www.google.com",
            proxies=proxies,
            timeout=10,
            headers={'User-Agent': 'TradingAgents-CN/1.0'}
        )
        
        if response.status_code == 200:
            print("✅ 代理连接成功")
            return True
        else:
            print(f"⚠️ 代理连接返回状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 代理连接失败: {e}")
        return False

def test_google_ai_direct():
    """测试直接Google AI API"""
    print("\n🧪 测试直接Google AI API")
    print("=" * 50)
    
    # 设置代理环境变量
    proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
    proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
    full_proxy_url = f"{proxy_url}:{proxy_port}"
    
    os.environ['HTTP_PROXY'] = full_proxy_url
    os.environ['HTTPS_PROXY'] = full_proxy_url
    
    print(f"🌐 使用代理: {full_proxy_url}")
    
    # 检查API密钥
    google_api_key = os.getenv('GOOGLE_API_KEY')
    if not google_api_key or google_api_key == 'your_google_api_key_here':
        print("❌ Google API密钥未配置")
        return False
    
    print(f"✅ Google API密钥已配置: {google_api_key[:20]}...")
    
    try:
        import google.generativeai as genai
        
        # 配置API密钥
        genai.configure(api_key=google_api_key)
        
        print("📝 测试生成内容...")
        
        # 创建模型实例
        model = genai.GenerativeModel('gemini-2.0-flash')
        
        # 生成内容
        response = model.generate_content("请用中文简单介绍一下苹果公司")
        
        if response and response.text:
            print("✅ Google AI API调用成功")
            print(f"   响应长度: {len(response.text)} 字符")
            print(f"   响应预览: {response.text[:100]}...")
            return True
        else:
            print("❌ Google AI API调用失败：无响应")
            return False
            
    except Exception as e:
        print(f"❌ Google AI API调用失败: {e}")
        return False

def test_langchain_with_proxy():
    """测试LangChain与代理"""
    print("\n🧪 测试LangChain与代理")
    print("=" * 50)
    
    # 设置代理环境变量
    proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
    proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
    full_proxy_url = f"{proxy_url}:{proxy_port}"
    
    os.environ['HTTP_PROXY'] = full_proxy_url
    os.environ['HTTPS_PROXY'] = full_proxy_url
    
    print(f"🌐 使用代理: {full_proxy_url}")
    
    google_api_key = os.getenv('GOOGLE_API_KEY')
    
    try:
        # 尝试使用Vertex AI替代
        from langchain_google_vertexai import ChatVertexAI
        
        print("📝 尝试使用Vertex AI...")
        
        # 注意：Vertex AI需要不同的认证方式
        # 这里只是测试导入是否成功
        print("✅ Vertex AI模块导入成功")
        print("💡 提示: Vertex AI需要Google Cloud项目配置")
        
        return True
        
    except ImportError:
        print("❌ Vertex AI模块未安装")
        
        # 回退到标准Google AI
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            print("📝 使用标准Google AI...")
            
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash",
                google_api_key=google_api_key,
                temperature=0.1
            )
            
            response = llm.invoke("请用中文说'你好'")
            
            if response and response.content:
                print("✅ LangChain Google AI调用成功")
                print(f"   响应: {response.content}")
                return True
            else:
                print("❌ LangChain Google AI调用失败")
                return False
                
        except Exception as e:
            print(f"❌ LangChain Google AI调用失败: {e}")
            return False
    
    except Exception as e:
        print(f"❌ LangChain测试失败: {e}")
        return False

def test_custom_adapter():
    """测试自定义适配器"""
    print("\n🧪 测试自定义代理适配器")
    print("=" * 50)
    
    try:
        from tradingagents.llm_adapters.google_adapter import ChatGoogleGenerativeAIWithProxy
        
        google_api_key = os.getenv('GOOGLE_API_KEY')
        proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
        proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
        
        print("📝 创建代理适配器...")
        
        llm = ChatGoogleGenerativeAIWithProxy(
            model="gemini-2.0-flash",
            google_api_key=google_api_key,
            proxy_url=proxy_url,
            proxy_port=proxy_port,
            temperature=0.1
        )
        
        print("✅ 代理适配器创建成功")
        
        # 测试连接
        print("📝 测试连接...")
        connection_success = llm.test_connection()
        
        if connection_success:
            print("✅ 连接测试成功")
            
            # 测试对话
            print("📝 测试对话...")
            response = llm.invoke("请用中文说'代理连接成功'")
            
            if response and response.content:
                print("✅ 对话测试成功")
                print(f"   响应: {response.content}")
                return True
            else:
                print("❌ 对话测试失败")
                return False
        else:
            print("❌ 连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 自定义适配器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Google AI代理简单测试")
    print("=" * 60)
    
    # 测试步骤
    tests = [
        ("基本代理连接", test_basic_proxy),
        ("Google AI直接API", test_google_ai_direct),
        ("LangChain集成", test_langchain_with_proxy),
        ("自定义适配器", test_custom_adapter)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n📊 测试结果总结")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(tests)} 测试通过")
    
    if success_count >= 3:
        print("\n🎉 代理配置基本成功！")
        print("💡 Google AI可以通过代理正常访问")
    elif success_count >= 1:
        print("\n⚠️ 部分功能正常，建议检查配置")
    else:
        print("\n❌ 所有测试失败，请检查:")
        print("1. 代理服务器是否运行在端口7890")
        print("2. Google API密钥是否有效")
        print("3. 网络连接是否正常")

if __name__ == "__main__":
    main()
