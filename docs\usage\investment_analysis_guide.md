# TradingAgents-CN 投资分析使用指南

## 🎯 概述

TradingAgents-CN 是一个基于多智能体大语言模型的投资分析框架，能够为您提供专业的股票分析报告。

## 🚀 快速开始

### 1. 基础配置

确保您已经配置了必要的API密钥：

```bash
# 检查配置状态
python -m cli.main config

# 运行集成测试
python -m cli.main test
```

### 2. 使用方式选择

#### 🌐 Web界面 (推荐新手)
```bash
# 启动Web界面
python -m streamlit run web/app.py
```
然后在浏览器中访问 `http://localhost:8501`

**优点**:
- 直观易用的图形界面
- 实时进度显示
- 详细的配置选项
- 结果可视化展示

**详细使用说明**: 请参考 [Web界面使用指南](web-interface-guide.md)

#### 💻 命令行界面 (适合开发者)
```bash
# 中文优化版本（推荐）
python examples/dashscope/demo_dashscope_chinese.py

# 完整功能版本
python examples/dashscope/demo_dashscope.py

# 简化测试版本
python examples/dashscope/demo_dashscope_simple.py
```

**优点**:
- 快速执行
- 易于自动化
- 适合批量处理

## 📊 分析内容详解

### 技术面分析
- **价格趋势**: 短期、中期、长期趋势判断
- **技术指标**: MA、MACD、RSI、布林带等
- **支撑阻力**: 关键价位和交易区间
- **成交量**: 量价关系分析

### 基本面分析
- **财务状况**: 营收、利润、现金流分析
- **业务结构**: 各业务板块表现
- **市场地位**: 竞争优势和市场份额
- **增长前景**: 未来发展机会

### 市场情绪分析
- **投资者情绪**: 市场参与者态度
- **分析师评级**: 专业机构观点
- **机构持仓**: 大资金动向
- **热点关注**: 市场焦点话题

### 风险评估
- **宏观风险**: 经济环境影响
- **行业风险**: 竞争和周期性风险
- **公司风险**: 特定经营风险
- **监管风险**: 政策变化影响

### 投资建议
- **评级建议**: 买入/持有/卖出
- **目标价位**: 短期和长期目标
- **时间框架**: 投资周期建议
- **风险控制**: 止损和仓位管理

## 🛠️ 自定义分析

### 修改分析参数

您可以通过修改示例程序来自定义分析：

```python
# 在 demo_dashscope_chinese.py 中修改
STOCK_SYMBOL = "TSLA"  # 改为您想分析的股票
ANALYSIS_DATE = "2024-06-26"  # 修改分析日期
```

### 支持的股票代码

- **美股**: AAPL, TSLA, MSFT, GOOGL, AMZN, NVDA 等
- **指数**: SPY, QQQ, DIA 等ETF
- **其他**: 大部分在美国交易所上市的股票

## 🎯 使用技巧

### 1. 选择合适的模型

```python
# 在配置中选择不同的模型
"deep_think_llm": "qwen-max",     # 最高质量，适合深度分析
"quick_think_llm": "qwen-plus",   # 平衡性能，日常使用
# "qwen-turbo" 适合快速查询
```

### 2. 分析不同时间段

```python
# 修改分析日期来分析历史表现
ANALYSIS_DATE = "2024-01-01"  # 年初分析
ANALYSIS_DATE = "2024-06-01"  # 半年度分析
```

### 3. 关注特定方面

您可以在提示词中强调特定分析方向：
- 技术面分析
- 基本面分析
- 风险评估
- 行业比较

## ⚠️ 重要提醒

### 投资风险提示

1. **仅供参考**: 分析结果仅供参考，不构成投资建议
2. **风险自担**: 投资有风险，决策需谨慎
3. **多方验证**: 建议结合其他信息源进行验证
4. **专业咨询**: 重大投资决策建议咨询专业财务顾问

### 数据准确性

1. **实时性**: 数据可能存在延迟，请以实际市场数据为准
2. **完整性**: AI分析可能遗漏某些重要信息
3. **准确性**: 预测和建议存在不确定性

## 🔧 故障排除

### 常见问题

1. **API密钥错误**: 检查.env文件中的密钥配置
2. **网络连接**: 确保网络连接正常
3. **模型响应慢**: 可以尝试使用qwen-turbo模型

### 获取帮助

```bash
# 查看帮助信息
python -m cli.main help

# 查看示例程序
python -m cli.main examples
```

## 📈 高级用法

### 批量分析

您可以修改程序来分析多只股票：

```python
stocks = ["AAPL", "MSFT", "GOOGL", "TSLA"]
for stock in stocks:
    # 运行分析逻辑
    analyze_stock(stock)
```

### 定期分析

设置定时任务来定期生成分析报告：

```bash
# 使用cron或Windows任务计划程序
# 每日运行分析
0 9 * * * python examples/dashscope/demo_dashscope_chinese.py
```

## 🎓 学习资源

### 推荐阅读

1. **技术分析**: 学习技术指标的含义和应用
2. **基本面分析**: 了解财务报表分析方法
3. **风险管理**: 掌握投资风险控制技巧
4. **市场心理**: 理解市场情绪和行为金融学

### 实践建议

1. **模拟交易**: 先用模拟账户练习
2. **小额试验**: 从小额投资开始
3. **持续学习**: 不断提升投资知识和技能
4. **记录总结**: 记录投资决策和结果，总结经验

---

*免责声明: 本工具仅用于教育和研究目的，不构成投资建议。投资有风险，决策需谨慎。*
