#!/usr/bin/env python3
"""
Google AI代理配置脚本
帮助在中国地区通过代理使用Google AI服务
"""

import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv(project_root / ".env", override=True)

def setup_proxy_environment():
    """设置代理环境变量"""
    print("🌐 设置代理环境变量")
    print("=" * 50)
    
    # 获取代理配置
    proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
    proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
    full_proxy_url = f"{proxy_url}:{proxy_port}"
    
    print(f"代理地址: {full_proxy_url}")
    
    # 设置环境变量
    proxy_vars = {
        'HTTP_PROXY': full_proxy_url,
        'HTTPS_PROXY': full_proxy_url,
        'http_proxy': full_proxy_url,
        'https_proxy': full_proxy_url
    }
    
    for var, value in proxy_vars.items():
        os.environ[var] = value
        print(f"✅ 设置 {var} = {value}")
    
    return full_proxy_url

def test_proxy_connection():
    """测试代理连接"""
    print("\n📡 测试代理连接")
    print("=" * 50)
    
    try:
        import requests
        
        proxy_url = os.environ.get('HTTP_PROXY')
        
        # 测试多个目标
        test_urls = [
            "https://www.google.com",
            "https://generativelanguage.googleapis.com",
            "https://ai.google.dev"
        ]
        
        success_count = 0
        
        for url in test_urls:
            try:
                print(f"📝 测试访问: {url}")
                
                response = requests.get(
                    url,
                    timeout=10,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                )
                
                if response.status_code == 200:
                    print(f"✅ 成功访问 {url}")
                    success_count += 1
                else:
                    print(f"⚠️ 访问 {url} 返回状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 访问 {url} 失败: {e}")
        
        print(f"\n📊 代理测试结果: {success_count}/{len(test_urls)} 成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 代理连接测试失败: {e}")
        return False

def configure_google_ai_client():
    """配置Google AI客户端"""
    print("\n🔧 配置Google AI客户端")
    print("=" * 50)
    
    try:
        import google.generativeai as genai
        import httpx
        
        # 获取API密钥
        google_api_key = os.getenv('GOOGLE_API_KEY')
        if not google_api_key or google_api_key == 'your_google_api_key_here':
            print("❌ Google API密钥未配置")
            return False
        
        print(f"✅ Google API密钥已配置: {google_api_key[:20]}...")
        
        # 配置代理客户端
        proxy_url = os.environ.get('HTTP_PROXY')
        
        if proxy_url:
            print(f"🌐 使用代理: {proxy_url}")
            
            # 创建带代理的HTTP客户端
            client = httpx.Client(
                proxies={
                    'http://': proxy_url,
                    'https://': proxy_url
                },
                timeout=30.0,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            # 配置genai使用自定义客户端
            genai.configure(
                api_key=google_api_key,
                client_options={'api_endpoint': 'https://generativelanguage.googleapis.com'}
            )
            
            print("✅ Google AI客户端配置完成")
            return True
        else:
            print("❌ 代理未配置")
            return False
            
    except ImportError as e:
        print(f"❌ 缺少必要的库: {e}")
        print("💡 请安装: pip install google-generativeai httpx")
        return False
    except Exception as e:
        print(f"❌ Google AI客户端配置失败: {e}")
        return False

def test_google_ai_api():
    """测试Google AI API"""
    print("\n🧪 测试Google AI API")
    print("=" * 50)
    
    try:
        import google.generativeai as genai
        
        # 简单的生成测试
        print("📝 测试内容生成...")
        
        model = genai.GenerativeModel('gemini-2.0-flash')
        
        # 使用简单的提示
        response = model.generate_content(
            "Say 'Hello from China' in Chinese",
            generation_config=genai.types.GenerationConfig(
                temperature=0.1,
                max_output_tokens=100
            )
        )
        
        if response and response.text:
            print("✅ Google AI API调用成功")
            print(f"   响应: {response.text}")
            return True
        else:
            print("❌ Google AI API调用失败：无响应")
            return False
            
    except Exception as e:
        print(f"❌ Google AI API测试失败: {e}")
        return False

def test_langchain_integration():
    """测试LangChain集成"""
    print("\n🧪 测试LangChain集成")
    print("=" * 50)
    
    try:
        # 尝试使用我们的自定义适配器
        from tradingagents.llm_adapters.google_adapter import ChatGoogleGenerativeAIWithProxy
        
        google_api_key = os.getenv('GOOGLE_API_KEY')
        proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
        proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
        
        print("📝 创建LangChain适配器...")
        
        llm = ChatGoogleGenerativeAIWithProxy(
            model="gemini-2.0-flash",
            google_api_key=google_api_key,
            proxy_url=proxy_url,
            proxy_port=proxy_port,
            temperature=0.1,
            max_tokens=100
        )
        
        print("✅ 适配器创建成功")
        
        # 测试对话
        print("📝 测试对话...")
        response = llm.invoke("请用中文说'你好，我来自中国'")
        
        if response and response.content:
            print("✅ LangChain集成测试成功")
            print(f"   响应: {response.content}")
            return True
        else:
            print("❌ LangChain集成测试失败")
            return False
            
    except Exception as e:
        print(f"❌ LangChain集成测试失败: {e}")
        return False

def create_usage_guide():
    """创建使用指南"""
    print("\n📖 创建使用指南")
    print("=" * 50)
    
    guide_content = """
# Google AI代理使用指南

## 🎯 配置要求

1. **代理服务器**: 确保代理服务器运行在端口7890
2. **API密钥**: 在.env文件中配置有效的Google API密钥
3. **网络环境**: 确保代理可以访问Google服务

## 🚀 使用方法

### 1. Web界面使用
```bash
python -m streamlit run web/app.py
```
- 在左侧边栏选择"Google AI - Gemini模型"
- 选择模型: gemini-2.0-flash (推荐)
- 启用记忆功能
- 开始分析

### 2. CLI使用
```bash
python -m cli.main --llm-provider google --model gemini-2.0-flash --stock AAPL
```

### 3. Python API使用
```python
from tradingagents.llm_adapters.google_adapter import ChatGoogleGenerativeAIWithProxy

llm = ChatGoogleGenerativeAIWithProxy(
    model="gemini-2.0-flash",
    proxy_url="http://127.0.0.1",
    proxy_port=7890
)

response = llm.invoke("分析苹果公司的投资价值")
```

## ⚠️ 注意事项

1. **代理稳定性**: 确保代理服务器稳定运行
2. **API限制**: 注意Google AI的API调用限制
3. **网络延迟**: 通过代理访问可能有额外延迟
4. **成本控制**: 监控API使用量和成本

## 🔧 故障排除

1. **连接失败**: 检查代理服务器状态
2. **API错误**: 验证API密钥有效性
3. **地区限制**: 确保代理IP地址支持Google AI服务
"""
    
    guide_path = project_root / "GOOGLE_AI_PROXY_GUIDE.md"
    
    try:
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"✅ 使用指南已创建: {guide_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建使用指南失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Google AI代理配置向导")
    print("=" * 60)
    
    # 步骤1: 设置代理环境
    proxy_url = setup_proxy_environment()
    
    # 步骤2: 测试代理连接
    proxy_success = test_proxy_connection()
    
    if not proxy_success:
        print("\n❌ 代理连接失败，请检查代理服务器")
        return
    
    # 步骤3: 配置Google AI客户端
    client_success = configure_google_ai_client()
    
    # 步骤4: 测试Google AI API
    api_success = False
    if client_success:
        api_success = test_google_ai_api()
    
    # 步骤5: 测试LangChain集成
    langchain_success = False
    if api_success:
        langchain_success = test_langchain_integration()
    
    # 步骤6: 创建使用指南
    guide_success = create_usage_guide()
    
    # 总结结果
    print(f"\n📊 配置结果总结")
    print("=" * 60)
    print(f"代理连接: {'✅ 成功' if proxy_success else '❌ 失败'}")
    print(f"客户端配置: {'✅ 成功' if client_success else '❌ 失败'}")
    print(f"API测试: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"LangChain集成: {'✅ 成功' if langchain_success else '❌ 失败'}")
    print(f"使用指南: {'✅ 已创建' if guide_success else '❌ 失败'}")
    
    if langchain_success:
        print("\n🎉 Google AI代理配置完全成功！")
        print("💡 现在可以在TradingAgents-CN中使用Google AI了")
        print(f"\n🚀 快速开始:")
        print("1. 启动Web界面: python -m streamlit run web/app.py")
        print("2. 选择Google AI作为LLM提供商")
        print("3. 开始股票分析")
    elif api_success:
        print("\n⚠️ 基本API功能正常，LangChain集成需要调试")
    else:
        print("\n❌ 配置失败，请检查:")
        print("1. 代理服务器是否正常运行")
        print("2. Google API密钥是否有效")
        print("3. 网络连接是否稳定")

if __name__ == "__main__":
    main()
