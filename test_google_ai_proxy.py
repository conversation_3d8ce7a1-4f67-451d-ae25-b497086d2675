#!/usr/bin/env python3
"""
测试Google AI代理配置
验证是否能够通过代理正常访问Google AI API
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_google_ai_proxy():
    """测试Google AI代理配置"""
    
    print("🧪 测试Google AI代理配置")
    print("=" * 60)
    
    try:
        # 导入我们的适配器
        from tradingagents.llm_adapters.google_adapter import (
            ChatGoogleGenerativeAIWithProxy, 
            create_google_ai_with_proxy
        )
        
        print("✅ 成功导入Google AI代理适配器")
        
        # 检查环境变量
        google_api_key = os.getenv('GOOGLE_API_KEY')
        proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
        proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
        
        print(f"📋 配置信息:")
        print(f"   Google API Key: {'已配置' if google_api_key and google_api_key != 'your_google_api_key_here' else '未配置'}")
        print(f"   代理地址: {proxy_url}:{proxy_port}")
        
        if not google_api_key or google_api_key == 'your_google_api_key_here':
            print("❌ Google API密钥未配置，请在.env文件中设置GOOGLE_API_KEY")
            return False
        
        # 创建代理适配器实例
        print("\n🔧 创建Google AI代理适配器...")
        llm = create_google_ai_with_proxy(
            model="gemini-2.0-flash",
            api_key=google_api_key,
            proxy_url=proxy_url,
            proxy_port=proxy_port,
            temperature=0.1,
            max_tokens=500
        )
        
        print("✅ Google AI代理适配器创建成功")
        
        # 测试连接
        print("\n🌐 测试代理连接...")
        connection_success = llm.test_connection()
        
        if not connection_success:
            print("❌ 代理连接测试失败")
            return False
        
        # 测试简单对话
        print("\n💬 测试简单对话...")
        try:
            response = llm.invoke("请用中文简单介绍一下你自己，不超过100字")
            
            if response and response.content:
                print("✅ 对话测试成功")
                print(f"   响应长度: {len(response.content)} 字符")
                print(f"   响应内容: {response.content[:200]}...")
                
                # 显示模型信息
                model_info = llm.get_model_info()
                print(f"\n📊 模型信息:")
                for key, value in model_info.items():
                    print(f"   {key}: {value}")
                
                return True
            else:
                print("❌ 对话测试失败：无响应内容")
                return False
                
        except Exception as e:
            print(f"❌ 对话测试失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所需依赖：pip install langchain-google-genai google-generativeai")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_direct_google_ai():
    """测试直接Google AI API（不通过LangChain）"""
    
    print("\n🧪 测试直接Google AI API")
    print("=" * 60)
    
    try:
        import google.generativeai as genai
        
        # 获取API密钥
        google_api_key = os.getenv('GOOGLE_API_KEY')
        if not google_api_key or google_api_key == 'your_google_api_key_here':
            print("❌ Google API密钥未配置")
            return False
        
        # 设置代理环境变量
        proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
        proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
        full_proxy_url = f"{proxy_url}:{proxy_port}"
        
        os.environ['HTTP_PROXY'] = full_proxy_url
        os.environ['HTTPS_PROXY'] = full_proxy_url
        
        print(f"🌐 设置代理: {full_proxy_url}")
        
        # 配置Google AI
        genai.configure(api_key=google_api_key)
        
        # 测试列出模型
        print("📋 获取可用模型列表...")
        models = list(genai.list_models())
        
        if models:
            print(f"✅ 成功获取 {len(models)} 个可用模型")
            print("   前5个模型:")
            for i, model in enumerate(models[:5]):
                print(f"   {i+1}. {model.name} - {model.display_name}")
        else:
            print("❌ 未获取到可用模型")
            return False
        
        # 测试简单生成
        print("\n💬 测试内容生成...")
        model = genai.GenerativeModel('gemini-2.0-flash')
        response = model.generate_content("请用中文说：你好，我是Gemini模型")
        
        if response and response.text:
            print("✅ 内容生成成功")
            print(f"   响应: {response.text}")
            return True
        else:
            print("❌ 内容生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 直接API测试失败: {e}")
        return False


def main():
    """主函数"""
    
    print("🚀 Google AI代理配置测试工具")
    print("=" * 60)
    
    # 测试1: 代理适配器
    adapter_success = test_google_ai_proxy()
    
    # 测试2: 直接API
    direct_success = test_direct_google_ai()
    
    # 总结
    print("\n📊 测试结果总结")
    print("=" * 60)
    print(f"代理适配器测试: {'✅ 成功' if adapter_success else '❌ 失败'}")
    print(f"直接API测试: {'✅ 成功' if direct_success else '❌ 失败'}")
    
    if adapter_success and direct_success:
        print("\n🎉 所有测试通过！Google AI代理配置正常")
        print("💡 现在可以在TradingAgents-CN中使用Google AI模型了")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查配置")
        print("💡 确保:")
        print("   1. 代理服务器正在运行 (端口7890)")
        print("   2. Google API密钥已正确配置")
        print("   3. 网络连接正常")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
