---
name: 📚 文档改进 / Documentation Improvement
about: 报告文档问题或建议改进 / Report documentation issues or suggest improvements
title: '[DOCS] '
labels: ['documentation', 'good-first-issue']
assignees: ''
---

## 📚 文档问题 / Documentation Issue

**问题类型 / Issue Type:**
- [ ] 🐛 文档错误 / Documentation Error
- [ ] 📝 内容缺失 / Missing Content
- [ ] 🔄 内容过时 / Outdated Content
- [ ] 🌐 翻译问题 / Translation Issue
- [ ] 💡 改进建议 / Improvement Suggestion
- [ ] 🎨 格式问题 / Formatting Issue
- [ ] 🔗 链接失效 / Broken Links
- [ ] 其他 / Other: ___________

## 📍 文档位置 / Document Location

**文件路径 / File Path:**
请指明具体的文档文件和位置。
```
例如: README.md 第123行
例如: docs/DOCKER_GUIDE.md 安装部分
```

**相关链接 / Related Links:**
如果是在线文档，请提供链接。

## 🔍 问题详情 / Issue Details

**当前内容 / Current Content:**
请引用或描述有问题的当前内容。

**问题描述 / Problem Description:**
详细描述文档中的问题。

**建议修改 / Suggested Changes:**
请提供您建议的修改内容。

## 💡 改进建议 / Improvement Suggestions

**缺失内容 / Missing Content:**
如果是内容缺失，请描述需要添加的内容。

**目标读者 / Target Audience:**
- [ ] 🆕 新手用户 / Beginner Users
- [ ] 👨‍💻 开发者 / Developers
- [ ] 🔧 系统管理员 / System Administrators
- [ ] 🎓 学习者 / Learners
- [ ] 所有用户 / All Users

**内容类型 / Content Type:**
- [ ] 📖 使用教程 / Usage Tutorial
- [ ] ⚙️ 安装指南 / Installation Guide
- [ ] 🔧 配置说明 / Configuration Instructions
- [ ] 🐳 Docker部署 / Docker Deployment
- [ ] 🤖 API文档 / API Documentation
- [ ] 💡 最佳实践 / Best Practices
- [ ] 🔍 故障排除 / Troubleshooting
- [ ] 📊 示例代码 / Code Examples
- [ ] 其他 / Other: ___________

## 🌐 多语言支持 / Multi-language Support

**语言问题 / Language Issues:**
- [ ] 中文翻译错误 / Chinese translation error
- [ ] 英文翻译错误 / English translation error
- [ ] 术语不一致 / Inconsistent terminology
- [ ] 缺少翻译 / Missing translation

**建议翻译 / Suggested Translation:**
如果是翻译问题，请提供正确的翻译。

## 📝 具体修改建议 / Specific Change Suggestions

**修改前 / Before:**
```markdown
当前的文档内容
Current documentation content
```

**修改后 / After:**
```markdown
建议的修改内容
Suggested modified content
```

## 🎯 用户体验 / User Experience

**遇到困难的场景 / Problematic Scenario:**
描述用户在什么情况下会遇到这个文档问题。

**期望的用户体验 / Expected User Experience:**
描述理想的用户阅读体验。

## 📊 优先级 / Priority

**重要性 / Importance:**
- [ ] 🔥 高优先级 / High Priority - 严重影响用户使用
- [ ] 🟡 中优先级 / Medium Priority - 影响用户体验
- [ ] 🟢 低优先级 / Low Priority - 小幅改进

**影响范围 / Impact Scope:**
- [ ] 🌍 影响所有用户 / Affects all users
- [ ] 👥 影响特定用户群 / Affects specific user group
- [ ] 🔧 影响开发者 / Affects developers
- [ ] 📱 影响特定平台 / Affects specific platform

## 🔗 相关资源 / Related Resources

**参考文档 / Reference Documentation:**
- 相关的官方文档
- 类似项目的文档示例
- 技术标准或规范

**相关Issues / Related Issues:**
- 相关的文档问题: #
- 相关的功能请求: #

## ✅ 检查清单 / Checklist

请确认您已经：
- [ ] 明确指出了文档位置
- [ ] 详细描述了问题
- [ ] 提供了改进建议
- [ ] 考虑了目标读者
- [ ] 检查了相关文档

## 🤝 贡献意愿 / Contribution Willingness

**是否愿意贡献 / Willing to Contribute:**
- [ ] ✅ 我愿意提交PR修复这个文档问题
- [ ] 📝 我可以提供内容，但需要他人协助格式化
- [ ] 💡 我只是提供建议，希望他人实施
- [ ] 🌐 我可以协助翻译工作

---

**感谢您帮助改进项目文档！**
**Thank you for helping improve the project documentation!**

## 📖 文档贡献指南 / Documentation Contribution Guide

1. **Fork项目** / Fork the project
2. **创建分支** / Create a branch: `git checkout -b docs/improve-xxx`
3. **修改文档** / Edit documentation
4. **提交PR** / Submit PR
5. **等待审核** / Wait for review

**文档规范 / Documentation Standards:**
- 使用Markdown格式
- 保持中英文对照
- 添加适当的示例
- 确保链接有效
