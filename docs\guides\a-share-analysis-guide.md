# A股分析使用指南 (v0.1.7)

## 🎯 概述

TradingAgents-CN 提供了完整的A股市场支持，通过集成多种数据源（Tushare、AKShare、通达信API），为用户提供实时、准确的A股数据分析能力。v0.1.7版本进一步优化了分析性能和报告质量。

## 🎉 v0.1.7 A股功能亮点

- 🐳 **Docker一键部署**: 简化A股分析环境搭建
- 📄 **专业报告导出**: 支持Word/PDF格式的A股分析报告
- 🧠 **DeepSeek优化**: 专为中文A股场景优化的AI模型
- 📊 **混合数据源**: Tushare历史数据 + AKShare实时数据
- 💰 **成本优化**: 大幅降低A股分析成本

## 🚀 快速开始

### 方式一：Docker部署 (推荐)

```bash
# 1. 克隆项目
git clone https://github.com/hsliuping/TradingAgents-CN.git
cd TradingAgents-CN

# 2. 配置环境变量
cp .env.example .env
# 编辑.env文件，添加API密钥

# 3. 一键启动
docker-compose up -d

# 4. 访问Web界面
# http://localhost:8501
```

### 方式二：本地部署

```bash
# 1. 激活虚拟环境
.\env\Scripts\Activate.ps1  # Windows
source env/bin/activate     # Linux/macOS

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动Web界面
streamlit run web/app.py
```

### 开始A股分析

在Web界面中：
1. 选择LLM模型（推荐DeepSeek V3）
2. 在股票代码输入框中输入A股代码
3. 选择分析深度和分析师类型
3. 选择分析师和研究深度
4. 点击"开始分析"

## 📊 支持的A股代码格式

### 主要板块代码规则

| 代码前缀 | 市场板块 | 示例代码 | 股票名称 |
|----------|----------|----------|----------|
| **000xxx** | 深圳主板 | 000001 | 平安银行 |
| **002xxx** | 深圳中小板 | 002415 | 海康威视 |
| **003xxx** | 深圳主板 | 003816 | 中国广核 |
| **300xxx** | 创业板 | 300750 | 宁德时代 |
| **600xxx** | 上海主板 | 600519 | 贵州茅台 |
| **601xxx** | 上海主板 | 601318 | 中国平安 |
| **603xxx** | 上海主板 | 603259 | 药明康德 |
| **688xxx** | 科创板 | 688981 | 中芯国际 |

### 热门股票代码示例

#### 🏦 银行股
- `000001` - 平安银行
- `600036` - 招商银行
- `601398` - 工商银行
- `601288` - 农业银行

#### 🍷 白酒股
- `600519` - 贵州茅台
- `000858` - 五粮液
- `000568` - 泸州老窖
- `002304` - 洋河股份

#### 🔋 新能源
- `300750` - 宁德时代
- `002594` - 比亚迪
- `300274` - 阳光电源
- `002460` - 赣锋锂业

#### 💻 科技股
- `000002` - 万科A
- `000651` - 格力电器
- `002415` - 海康威视
- `000725` - 京东方A

## 🔧 数据源说明

### Tushare数据接口优势

| 特性 | Tushare数据接口 | Yahoo Finance | 优势说明 |
|------|-----------|---------------|----------|
| **A股覆盖** | ✅ 完整覆盖 | ❌ 不支持 | 独有的A股数据源 |
| **实时性** | ✅ 秒级更新 | ⚠️ 15分钟延迟 | 适合日内交易分析 |
| **中文支持** | ✅ 原生中文 | ❌ 英文为主 | 股票名称、板块中文显示 |
| **成本** | ✅ 完全免费 | ✅ 免费 | 无API调用限制 |
| **配置复杂度** | ✅ 零配置 | ✅ 零配置 | 即装即用 |

### 可用服务器

系统自动使用经过测试的可用服务器：
- 武汉电信主站1 (119.97.185.59:7709)
- 广州双线主站7 (116.205.183.150:7709)
- 广州双线主站6 (116.205.171.132:7709)
- 北京双线主站4 (124.70.75.113:7709)
- 等10个验证可用的服务器

## 📈 分析功能特色

### 1. 实时行情数据

- **当前价格**: 实时股价更新
- **涨跌幅**: 当日涨跌幅度和涨跌金额
- **成交量**: 实时成交量和成交额
- **五档买卖**: 买一到买五、卖一到卖五价格和数量

### 2. 历史数据分析

- **K线数据**: 日线、周线、月线历史数据
- **技术指标**: MA、RSI、MACD、布林带等
- **价格走势**: 历史价格变化趋势分析
- **成交量分析**: 量价关系分析

### 3. A股特色分析

- **涨跌停分析**: 识别涨停、跌停等A股特有现象
- **ST股票识别**: 特别处理股票的风险提示
- **板块轮动**: A股特有的板块轮动规律分析
- **政策影响**: 中国政策对股价的影响分析

## 🕐 交易时间说明

### A股交易时间

- **上午**: 09:30 - 11:30
- **下午**: 13:00 - 15:00
- **休市**: 周末、法定节假日

### 数据获取说明

| 时间段 | 数据类型 | 说明 |
|--------|----------|------|
| **交易时间** | 实时数据 | 秒级更新的实时行情 |
| **非交易时间** | 收盘数据 | 显示最后交易日的收盘价 |
| **周末/节假日** | 历史数据 | 可获取历史K线和技术指标 |

## 🎯 使用示例

### 示例1: 分析贵州茅台

1. 选择市场: **A股**
2. 输入代码: **600519**
3. 选择分析师: **全部分析师**
4. 研究深度: **深度分析**
5. 点击"开始分析"

**预期结果**:
- 实时股价和涨跌幅
- 技术指标分析
- 基本面评估
- 新闻和社交媒体情绪
- 综合投资建议

### 示例2: 分析宁德时代

1. 选择市场: **A股**
2. 输入代码: **300750**
3. 选择分析师: **技术分析师 + 基本面分析师**
4. 研究深度: **标准分析**
5. 点击"开始分析"

**预期结果**:
- 创业板股票特色分析
- 新能源行业趋势
- 技术形态识别
- 估值水平评估

## ⚠️ 注意事项

### 1. 网络要求

- 需要稳定的网络连接访问数据服务器
- 如果连接失败，系统会自动尝试备用服务器
- 建议在网络环境良好时进行分析

### 2. 数据准确性

- 实时数据来源于通达信免费服务器
- 重要投资决策建议交叉验证多个数据源
- 系统提供的是分析建议，不构成投资建议

### 3. 使用限制

- Tushare数据接口为免费服务，可能存在访问限制
- 建议合理使用，避免频繁请求
- 如遇到连接问题，可稍后重试

## 🔧 故障排除

### 常见问题

#### 1. 连接失败

**问题**: 显示"Tushare数据接口连接失败"

**解决方案**:
```bash
# 检查网络连接
ping 119.97.185.59

# 重新测试服务器
python tests/fast_tdx_test.py

# 检查防火墙设置
```

#### 2. 数据获取失败

**问题**: 连接成功但无法获取数据

**解决方案**:
- 确认股票代码格式正确
- 检查是否为交易时间
- 尝试其他股票代码验证

#### 3. 分析速度慢

**问题**: A股分析比美股慢

**解决方案**:
- 选择较少的分析师
- 降低研究深度
- 检查网络连接质量

## 📊 性能优化建议

### 1. 分析师选择

- **快速分析**: 选择1-2个核心分析师
- **全面分析**: 选择所有分析师（耗时较长）
- **专项分析**: 根据需求选择特定分析师

### 2. 研究深度

- **快速**: 2-4分钟，适合日内交易
- **标准**: 5-8分钟，适合短期投资
- **深度**: 10-15分钟，适合长期投资
- **全面**: 15-25分钟，适合重要决策

### 3. 使用技巧

- 在交易时间进行分析获得最新数据
- 批量分析多只股票时适当间隔
- 保存分析结果供后续参考

## 🎉 总结

TradingAgents-CN v0.1.3 的A股支持为中国投资者提供了：

1. **🇨🇳 本土化体验**: 完整的A股数据覆盖
2. **⚡ 实时分析**: 秒级数据更新
3. **💰 零成本**: 免费的数据源
4. **🔧 易用性**: 零配置即用
5. **📊 专业性**: 多维度分析框架

现在您可以像分析美股一样，对A股进行专业的多智能体协作分析！
