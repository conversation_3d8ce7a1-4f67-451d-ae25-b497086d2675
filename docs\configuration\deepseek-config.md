# DeepSeek V3配置指南

## 📋 概述

DeepSeek V3是一个性能强大、性价比极高的大语言模型，在推理、代码生成和中文理解方面表现优秀。本指南将详细介绍如何在TradingAgents中配置和使用DeepSeek V3。

## 🎯 v0.1.5 新增功能

- ✅ **完整的DeepSeek V3集成**：支持全系列模型
- ✅ **工具调用支持**：完整的Function Calling功能
- ✅ **OpenAI兼容API**：使用标准OpenAI接口
- ✅ **Web界面支持**：在Web界面中选择DeepSeek模型
- ✅ **智能体协作**：支持多智能体协作分析

## 🔑 获取API密钥

### 第一步：注册DeepSeek账号
1. 访问 [DeepSeek平台](https://platform.deepseek.com/)
2. 点击"Sign Up"注册账号
3. 使用邮箱或手机号完成注册
4. 验证邮箱或手机号

### 第二步：获取API密钥
1. 登录DeepSeek控制台
2. 进入"API Keys"页面
3. 点击"Create API Key"
4. 设置密钥名称（如：TradingAgents）
5. 复制生成的API密钥（格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx）

## ⚙️ 配置步骤

### 1. 环境变量配置

在项目根目录的`.env`文件中添加：

```bash
# DeepSeek V3配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_ENABLED=true
```

### 2. 支持的模型

| 模型名称 | 说明 | 适用场景 | 上下文长度 | 推荐度 |
|---------|------|---------|-----------|--------|
| **deepseek-chat** | 通用对话模型 | 股票投资分析、推荐使用 | 128K | ⭐⭐⭐⭐⭐ |

**说明**：
- ✅ **deepseek-chat**：最适合股票投资分析，平衡了技术分析和自然语言表达
- ⚠️ **deepseek-coder**：虽然支持工具调用，但专注代码任务，在投资建议表达方面不如通用模型
- ❌ **deepseek-reasoner**：不支持工具调用，不适用于TradingAgents的智能体架构

### 3. Web界面配置

1. 启动Web界面：`streamlit run web/app.py`
2. 进入"配置管理"页面
3. 在"模型配置"中找到DeepSeek模型
4. 填入API Key
5. 启用相应的模型

## 🛠️ 使用方法

### 1. CLI使用

```bash
# 启动CLI
python -m cli.main

# 选择DeepSeek V3作为LLM提供商
# 选择DeepSeek模型
# 开始分析
```

### 2. Web界面使用

1. 在分析页面选择DeepSeek模型
2. 输入股票代码
3. 选择分析深度
4. 开始分析

### 3. 编程接口

```python
from tradingagents.llm.deepseek_adapter import create_deepseek_adapter

# 创建DeepSeek适配器
adapter = create_deepseek_adapter(model="deepseek-chat")

# 获取模型信息
info = adapter.get_model_info()
print(f"使用模型: {info['model']}")

# 创建智能体
from langchain.tools import tool

@tool
def get_stock_price(symbol: str) -> str:
    """获取股票价格"""
    return f"股票{symbol}的价格信息"

agent = adapter.create_agent(
    tools=[get_stock_price],
    system_prompt="你是股票分析专家"
)

# 执行分析
result = agent.invoke({"input": "分析AAPL股票"})
print(result["output"])
```

## 🎯 最佳实践

### 1. 模型选择建议

- **日常分析**：使用deepseek-chat，通用性强，性价比高
- **逻辑分析**：使用deepseek-coder，逻辑推理能力强
- **深度推理**：使用deepseek-reasoner，复杂问题分析
- **长文本**：优先使用deepseek-chat，支持128K上下文

### 2. 参数调优

```python
# 推荐的参数设置
adapter = create_deepseek_adapter(
    model="deepseek-chat",
    temperature=0.1,  # 降低随机性，提高一致性
    max_tokens=2000   # 适中的输出长度
)
```

### 3. 成本控制

- DeepSeek V3价格极低，约为GPT-4的1/10
- 输入：¥0.14/百万tokens
- 输出：¥0.28/百万tokens
- 适合大量使用，成本压力小

## 🔍 故障排除

### 常见问题

#### 1. API密钥错误
```
错误：Authentication failed
解决：检查API Key是否正确，确保以sk-开头
```

#### 2. 网络连接问题
```
错误：Connection timeout
解决：检查网络连接，确保可以访问api.deepseek.com
```

#### 3. 配置未生效
```
错误：DeepSeek not enabled
解决：确保DEEPSEEK_ENABLED=true
```

### 调试方法

1. **检查配置**：
```python
from tradingagents.llm.deepseek_adapter import DeepSeekAdapter
print(DeepSeekAdapter.is_available())
```

2. **测试连接**：
```bash
python tests/test_deepseek_integration.py
```

3. **查看日志**：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 性能对比

| 指标 | DeepSeek V3 | GPT-4 | Claude-3 | 阿里百炼 |
|------|-------------|-------|----------|---------|
| **推理能力** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **中文理解** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **工具调用** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **响应速度** | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **成本效益** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| **稳定性** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 💰 定价优势

### DeepSeek V3定价
- **输入**：¥0.14/百万tokens
- **输出**：¥0.28/百万tokens
- **平均**：约¥0.21/百万tokens

### 成本对比
- **vs GPT-4**：便宜约90%
- **vs Claude-3**：便宜约85%
- **vs 阿里百炼**：便宜约50%

### 实际使用成本
- **日常分析**：约¥0.01/次
- **深度分析**：约¥0.05/次
- **月度使用**：约¥10-50（重度使用）

## 🎉 总结

DeepSeek V3为TradingAgents提供了：

- 🧠 **强大的推理能力**：媲美GPT-4的分析水平
- 💰 **极高的性价比**：成本仅为GPT-4的1/10
- 🛠️ **完整的工具支持**：Function Calling功能完善
- 🇨🇳 **优秀的中文能力**：专门优化的中文理解
- 📊 **专业的分析能力**：适合金融数据分析
- 🚀 **快速的响应速度**：API响应稳定快速

通过DeepSeek V3，您可以享受到高质量、低成本的AI股票分析服务！
