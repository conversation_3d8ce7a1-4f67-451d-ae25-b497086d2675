#!/usr/bin/env python3
"""
测试Google AI代理配置
基于项目现有的测试框架，添加代理支持测试
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv(project_root / ".env", override=True)

def test_proxy_configuration():
    """测试代理配置"""
    print("🌐 测试代理配置")
    print("=" * 50)
    
    # 获取代理配置
    proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
    proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
    
    print(f"代理地址: {proxy_url}:{proxy_port}")
    
    # 设置代理环境变量
    full_proxy_url = f"{proxy_url}:{proxy_port}"
    os.environ['HTTP_PROXY'] = full_proxy_url
    os.environ['HTTPS_PROXY'] = full_proxy_url
    
    print(f"✅ 代理环境变量已设置: {full_proxy_url}")
    
    # 测试代理连接
    try:
        import requests
        print("📡 测试代理连接...")
        
        response = requests.get(
            "https://www.google.com",
            proxies={'http': full_proxy_url, 'https': full_proxy_url},
            timeout=10,
            headers={'User-Agent': 'TradingAgents-CN/1.0'}
        )
        
        if response.status_code == 200:
            print("✅ 代理连接测试成功")
            return True
        else:
            print(f"⚠️ 代理连接返回状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 代理连接测试失败: {e}")
        return False

def test_google_ai_with_proxy():
    """测试通过代理使用Google AI"""
    try:
        print("\n🧪 测试Google AI代理访问")
        print("=" * 50)
        
        # 检查API密钥
        google_api_key = os.getenv('GOOGLE_API_KEY')
        if not google_api_key or google_api_key == 'your_google_api_key_here':
            print("❌ Google API密钥未配置")
            return False
        
        print(f"✅ Google API密钥已配置: {google_api_key[:20]}...")
        
        # 设置代理
        proxy_url = os.getenv('HTTP_PROXY_URL', 'http://127.0.0.1')
        proxy_port = int(os.getenv('HTTP_PROXY_PORT', '7890'))
        full_proxy_url = f"{proxy_url}:{proxy_port}"
        
        os.environ['HTTP_PROXY'] = full_proxy_url
        os.environ['HTTPS_PROXY'] = full_proxy_url
        
        print(f"🌐 使用代理: {full_proxy_url}")
        
        # 测试1: 直接Google AI API
        print("\n📝 测试直接Google AI API...")
        try:
            import google.generativeai as genai
            
            genai.configure(api_key=google_api_key)
            
            # 尝试列出模型
            models = list(genai.list_models())
            
            if models:
                print(f"✅ 成功获取 {len(models)} 个可用模型")
                print("   前3个模型:")
                for i, model in enumerate(models[:3]):
                    print(f"   {i+1}. {model.name}")
                direct_success = True
            else:
                print("❌ 未获取到可用模型")
                direct_success = False
                
        except Exception as e:
            print(f"❌ 直接API测试失败: {e}")
            direct_success = False
        
        # 测试2: LangChain集成
        print("\n📝 测试LangChain集成...")
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash",
                temperature=0.1,
                max_tokens=500,
                google_api_key=google_api_key
            )
            
            response = llm.invoke("请用中文简单介绍一下你自己")
            
            if response and response.content:
                print("✅ LangChain集成测试成功")
                print(f"   响应长度: {len(response.content)} 字符")
                print(f"   响应预览: {response.content[:100]}...")
                langchain_success = True
            else:
                print("❌ LangChain集成测试失败：无响应")
                langchain_success = False
                
        except Exception as e:
            print(f"❌ LangChain集成测试失败: {e}")
            langchain_success = False
        
        # 测试3: 我们的代理适配器
        print("\n📝 测试自定义代理适配器...")
        try:
            from tradingagents.llm_adapters.google_adapter import ChatGoogleGenerativeAIWithProxy
            
            llm_proxy = ChatGoogleGenerativeAIWithProxy(
                model="gemini-2.0-flash",
                google_api_key=google_api_key,
                temperature=0.1,
                max_tokens=500,
                proxy_url=proxy_url,
                proxy_port=proxy_port
            )
            
            # 测试连接
            connection_test = llm_proxy.test_connection()
            
            if connection_test:
                print("✅ 代理适配器连接测试成功")
                
                # 测试对话
                response = llm_proxy.invoke("请用中文分析一下当前AI技术的发展趋势")
                
                if response and response.content:
                    print("✅ 代理适配器对话测试成功")
                    print(f"   响应长度: {len(response.content)} 字符")
                    print(f"   响应预览: {response.content[:100]}...")
                    
                    # 显示模型信息
                    model_info = llm_proxy.get_model_info()
                    print(f"\n📊 模型信息:")
                    for key, value in model_info.items():
                        print(f"   {key}: {value}")
                    
                    proxy_adapter_success = True
                else:
                    print("❌ 代理适配器对话测试失败")
                    proxy_adapter_success = False
            else:
                print("❌ 代理适配器连接测试失败")
                proxy_adapter_success = False
                
        except Exception as e:
            print(f"❌ 代理适配器测试失败: {e}")
            proxy_adapter_success = False
        
        return direct_success, langchain_success, proxy_adapter_success
        
    except Exception as e:
        print(f"❌ Google AI代理测试失败: {e}")
        return False, False, False

def test_tradingagents_with_proxy():
    """测试TradingAgents中使用代理的Google AI"""
    try:
        print("\n🧪 测试TradingAgents中使用代理Google AI")
        print("=" * 50)
        
        from tradingagents.graph.trading_graph import TradingAgentsGraph
        from tradingagents.default_config import DEFAULT_CONFIG
        
        # 创建配置
        config = DEFAULT_CONFIG.copy()
        config["llm_provider"] = "google"
        config["deep_think_llm"] = "gemini-2.0-flash"
        config["quick_think_llm"] = "gemini-2.0-flash"
        config["online_tools"] = False  # 避免API限制
        config["memory_enabled"] = True
        
        # 修复路径
        config["data_dir"] = str(project_root / "data")
        config["results_dir"] = str(project_root / "results")
        config["data_cache_dir"] = str(project_root / "tradingagents" / "dataflows" / "data_cache")
        
        # 创建目录
        os.makedirs(config["data_dir"], exist_ok=True)
        os.makedirs(config["results_dir"], exist_ok=True)
        os.makedirs(config["data_cache_dir"], exist_ok=True)
        
        print("✅ 配置创建成功")
        
        # 创建TradingAgentsGraph实例
        print("🚀 初始化TradingAgents图...")
        graph = TradingAgentsGraph(["market"], config=config, debug=False)
        
        print("✅ TradingAgents图初始化成功")
        
        # 测试简单分析
        print("📊 测试股票分析...")
        try:
            state, decision = graph.propagate("AAPL", "2025-06-27")
            
            if state and decision:
                print("✅ 代理Google AI驱动的股票分析成功")
                print(f"   决策结果: {decision}")
                
                if "market_report" in state and state["market_report"]:
                    market_report = state["market_report"]
                    print(f"   市场报告长度: {len(market_report)} 字符")
                    print(f"   报告预览: {market_report[:100]}...")
                
                return True
            else:
                print("❌ 分析完成但结果为空")
                return False
                
        except Exception as e:
            print(f"❌ 股票分析失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ TradingAgents代理测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Google AI代理配置完整测试")
    print("=" * 70)
    
    # 测试1: 代理配置
    proxy_success = test_proxy_configuration()
    
    # 测试2: Google AI代理访问
    if proxy_success:
        direct_success, langchain_success, proxy_adapter_success = test_google_ai_with_proxy()
    else:
        print("⚠️ 跳过Google AI测试（代理配置失败）")
        direct_success = langchain_success = proxy_adapter_success = False
    
    # 测试3: TradingAgents集成
    if proxy_adapter_success:
        tradingagents_success = test_tradingagents_with_proxy()
    else:
        print("⚠️ 跳过TradingAgents测试（代理适配器失败）")
        tradingagents_success = False
    
    # 总结结果
    print(f"\n📊 测试结果总结")
    print("=" * 70)
    print(f"代理配置: {'✅ 成功' if proxy_success else '❌ 失败'}")
    print(f"直接Google AI API: {'✅ 成功' if direct_success else '❌ 失败'}")
    print(f"LangChain集成: {'✅ 成功' if langchain_success else '❌ 失败'}")
    print(f"代理适配器: {'✅ 成功' if proxy_adapter_success else '❌ 失败'}")
    print(f"TradingAgents集成: {'✅ 成功' if tradingagents_success else '❌ 失败'}")
    
    success_count = sum([proxy_success, direct_success, langchain_success, proxy_adapter_success, tradingagents_success])
    
    if success_count >= 4:
        print("\n🎉 Google AI代理配置完全成功！")
        print("💡 现在可以在国内通过代理正常使用Google AI模型了")
        print("\n🚀 使用方法:")
        print("1. 确保代理服务器运行在端口7890")
        print("2. 在Web界面选择Google作为LLM提供商")
        print("3. 选择gemini-2.0-flash模型")
        print("4. 开始股票分析")
    elif success_count >= 2:
        print("\n⚠️ 部分功能正常，建议检查网络和代理配置")
    else:
        print("\n❌ 大部分测试失败，请检查:")
        print("1. 代理服务器是否正在运行")
        print("2. Google API密钥是否有效")
        print("3. 网络连接是否正常")

if __name__ == "__main__":
    main()
